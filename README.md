# 中国地图动态辐射效果 - 海洋蓝主题

一个使用Python创建的高大上中国地图动态效果项目，展示从广东总部向全国各省份辐射的抛物线动画。

## 🎯 效果特点

### 海洋蓝主题版本 (`china_map_animation.py`)
- 🌊 **海洋蓝主题**：深海蓝背景配色，视觉效果震撼
- 🚀 **3波次扩散**：按距离远近分3波连接，更有层次感
- 📊 **省份名字显示**：地图上清晰显示所有省份标签
- 💫 **闪烁总部标记**：粉色闪烁点突出广东总部位置
- 💎 **智能高亮**：当前波次连接的省份用金色高亮显示
- ⚡ **动态抛物线**：青绿色流光抛物线，弧度优美
- 🎪 **自动播放**：每3秒切换一个波次，节奏适中

## 🛠️ 技术栈

- **pyecharts**: 交互式图表库
- **numpy**: 数学计算
- **pandas**: 数据处理

## 🚀 快速开始

### 方法一：使用演示脚本（推荐）
```bash
python run_demo.py
```

### 方法二：手动运行

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **运行海洋蓝主题版本**
```bash
python china_map_animation.py
```

## 📁 文件结构

```
├── requirements.txt                  # 依赖包列表
├── china_map_animation.py           # 海洋蓝主题动画
├── run_demo.py                      # 快速演示脚本
├── README.md                        # 项目说明
└── 生成的HTML文件/
    └── china_map_ocean_theme.html   # 海洋蓝主题输出
```

## 🎨 海洋蓝主题配色

- **背景色**：深海蓝 (#0c1445) - 营造深邃海洋感
- **地图色**：海军蓝 (#1e3a8a) - 突出中国轮廓
- **边框色**：亮蓝色 (#3b82f6) - 清晰的省份边界
- **连接线**：青绿色 (#06ffa5) - 流光溢彩的抛物线
- **总部标记**：粉色 (#fb7185) - 醒目的闪烁效果
- **高亮省份**：金色 (#fbbf24) - 当前波次省份标记

## 🎯 自定义配置

### 修改总部位置
```python
self.headquarters = [经度, 纬度]  # 默认广州 [113.2644, 23.1291]
```

### 添加新省份
```python
self.provinces["新省份"] = [经度, 纬度]
```

### 调整动画速度
```python
play_interval=3000  # 毫秒，数值越小动画越快
```

### 修改抛物线弧度
```python
curve=0.4  # 0-1之间，数值越大弧度越大
```

### 调整波次数量
```python
# 在 generate_wave_data 函数中修改分组逻辑
wave_size = total_provinces // 3  # 改为其他数字可调整波次
```

## 🎪 动画效果说明

1. **总部标记**: 粉色闪烁圆点，表示广东总部位置
2. **当前省份**: 金色高亮圆点，当前波次连接的省份
3. **其他省份**: 青色暗淡圆点，未连接的省份
4. **连接线**: 青绿色动态抛物线，带有流光箭头效果
5. **3波次扩散**: 按距离远近分3波连接，层次分明
6. **省份标签**: 地图上显示所有省份名称
7. **时间轴**: 自动播放，可手动控制进度

## 💡 使用场景

- 🏢 企业业务网络展示
- 📊 数据流向可视化
- 🌐 物流网络演示
- 📈 市场覆盖展示
- 🎯 战略布局可视化

## 🔧 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install pyecharts numpy pandas
   ```

2. **中文显示问题**
   - 确保系统支持UTF-8编码
   - 浏览器设置为UTF-8编码

3. **动画不播放**
   - 检查浏览器JavaScript是否启用
   - 尝试使用Chrome或Firefox浏览器

## 📝 更新日志

- **v1.0**: 基础动画功能
- **v2.0**: 添加高级主题和波浪扩散
- **v3.0**: 精简为海洋蓝主题，3波次扩散，显示省份名字

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License - 可自由使用和修改
