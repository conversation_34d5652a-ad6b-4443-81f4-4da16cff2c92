# 中国地图动态辐射效果

一个使用Python创建的高大上中国地图动态效果项目，展示从广东总部向全国各省份辐射的抛物线动画。

## 🎯 效果特点

### 基础版本 (`china_map_animation.py`)
- ✨ 从广东总部向全国各省份发射动态抛物线
- 🎪 自动播放时间轴动画
- 💫 闪烁的总部标记点
- 🌊 流动的抛物线轨迹
- 🎨 深色科技风主题

### 高级版本 (`advanced_china_map.py`)
- 🚀 **波浪式扩散**：从近到远逐步连接城市
- 🎨 **三种主题**：科技、商务、海洋风格
- 📊 **智能连接**：基于城市GDP数据计算连接强度
- 💎 **多重特效**：抛物线、闪烁、流光、渐变
- 🏙️ **真实数据**：包含20个主要城市的经济数据
- 📈 **可视化图例**：城市规模和连接强度展示

## 🛠️ 技术栈

- **pyecharts**: 交互式图表库
- **numpy**: 数学计算
- **pandas**: 数据处理

## 🚀 快速开始

### 方法一：使用演示脚本（推荐）
```bash
python run_demo.py
```

### 方法二：手动运行

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **运行基础版本**
```bash
python china_map_animation.py
```

3. **运行高级版本**
```bash
python advanced_china_map.py
```

## 📁 文件结构

```
├── requirements.txt              # 依赖包列表
├── china_map_animation.py        # 基础版本动画
├── advanced_china_map.py         # 高级版本动画
├── run_demo.py                   # 快速演示脚本
├── README.md                     # 项目说明
└── 生成的HTML文件/
    ├── china_map_animation.html      # 基础版本输出
    ├── china_map_tech_theme.html     # 科技主题版本
    └── china_map_multi_themes.html   # 多主题完整版本
```

## 🎨 主题风格

### 科技主题 (Tech)
- 深蓝色背景 (#0a0e27)
- 青色连接线 (#00d4ff)
- 红色总部标记 (#ff6b6b)

### 商务主题 (Business)
- 深灰色背景 (#1a1a1a)
- 金色连接线 (#ffd700)
- 红色总部标记 (#ff4757)

### 海洋主题 (Ocean)
- 深海蓝背景 (#0c1445)
- 青绿色连接线 (#06ffa5)
- 粉色总部标记 (#fb7185)

## 🎯 自定义配置

### 修改总部位置
```python
self.headquarters = [经度, 纬度]  # 默认广州
```

### 添加新城市
```python
self.cities_data["新城市"] = {
    "coord": [经度, 纬度], 
    "gdp": GDP数值, 
    "population": 人口数值
}
```

### 调整动画速度
```python
play_interval=2000  # 毫秒，数值越小动画越快
```

### 修改抛物线弧度
```python
curve=0.3  # 0-1之间，数值越大弧度越大
```

## 🎪 动画效果说明

1. **总部标记**: 红色闪烁圆点，表示广东总部位置
2. **城市节点**: 蓝绿色圆点，大小根据城市GDP调整
3. **连接线**: 动态抛物线，带有流光效果
4. **波浪扩散**: 按距离远近分批次连接城市
5. **时间轴**: 自动播放，可手动控制进度

## 💡 使用场景

- 🏢 企业业务网络展示
- 📊 数据流向可视化
- 🌐 物流网络演示
- 📈 市场覆盖展示
- 🎯 战略布局可视化

## 🔧 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install pyecharts numpy pandas
   ```

2. **中文显示问题**
   - 确保系统支持UTF-8编码
   - 浏览器设置为UTF-8编码

3. **动画不播放**
   - 检查浏览器JavaScript是否启用
   - 尝试使用Chrome或Firefox浏览器

## 📝 更新日志

- **v1.0**: 基础动画功能
- **v2.0**: 添加高级主题和波浪扩散
- **v2.1**: 优化性能和视觉效果

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License - 可自由使用和修改
