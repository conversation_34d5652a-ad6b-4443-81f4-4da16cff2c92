#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版中国地图动态抛物线效果
确保与最新版pyecharts兼容
"""

from pyecharts import options as opts
from pyecharts.charts import Geo, Timeline
from pyecharts.globals import ChartType, SymbolType
import random

class SimpleChinaMap:
    def __init__(self):
        # 广东省坐标（总部位置）
        self.headquarters = [113.2644, 23.1291]  # 广州坐标
        
        # 主要城市坐标
        self.cities = {
            "广州": [113.2644, 23.1291],
            "北京": [116.4074, 39.9042],
            "上海": [121.4737, 31.2304],
            "深圳": [114.0579, 22.5431],
            "杭州": [120.1551, 30.2741],
            "南京": [118.7674, 32.0415],
            "武汉": [114.2919, 30.5844],
            "成都": [104.0665, 30.5723],
            "西安": [108.9540, 34.2658],
            "重庆": [106.5516, 29.5630],
            "天津": [117.1901, 39.1084],
            "苏州": [120.6197, 31.3089],
            "青岛": [120.3826, 36.0671],
            "长沙": [112.9836, 28.1127],
            "宁波": [121.5440, 29.8683],
            "郑州": [113.6401, 34.7566],
            "济南": [117.0009, 36.6758],
            "合肥": [117.2272, 31.8206],
            "福州": [119.2965, 26.0745],
            "厦门": [118.0894, 24.4798],
        }
    
    def generate_connections(self, step):
        """生成连接数据"""
        cities_list = list(self.cities.keys())
        if "广州" in cities_list:
            cities_list.remove("广州")
        
        # 每步显示不同数量的连接
        num_connections = min(3 + step * 2, len(cities_list))
        selected_cities = random.sample(cities_list, num_connections)
        
        # 生成连接线数据
        lines_data = []
        for city in selected_cities:
            lines_data.append(["广州", city])
        
        return lines_data
    
    def create_geo_map(self, step):
        """创建地理图表"""
        geo = (
            Geo(init_opts=opts.InitOpts(
                width="1200px", 
                height="800px",
                bg_color="#0f1419"
            ))
            .add_schema(
                maptype="china",
                itemstyle_opts=opts.ItemStyleOpts(
                    color="#323c48", 
                    border_color="#404a59"
                ),
                label_opts=opts.LabelOpts(is_show=False),
                emphasis_itemstyle_opts=opts.ItemStyleOpts(
                    color="#389bb7"
                )
            )
        )
        
        # 添加城市坐标
        for city, coord in self.cities.items():
            geo.add_coordinate(city, coord[0], coord[1])
        
        # 生成连接数据
        lines_data = self.generate_connections(step)
        
        # 添加连接线
        if lines_data:
            geo.add(
                series_name="连接线",
                data_pair=lines_data,
                type_=ChartType.LINES,
                effect_opts=opts.EffectOpts(
                    is_show=True,
                    symbol=SymbolType.ARROW,
                    symbol_size=6,
                    color="#ff6b6b"
                ),
                linestyle_opts=opts.LineStyleOpts(
                    curve=0.3,
                    opacity=0.8,
                    width=2,
                    color="#4ecdc4"
                )
            )
        
        # 添加总部标记
        geo.add(
            series_name="总部",
            data_pair=[("广州", 100)],
            type_=ChartType.SCATTER,
            symbol_size=20,
            itemstyle_opts=opts.ItemStyleOpts(
                color="#ff6b6b",
                border_color="#fff",
                border_width=2
            )
        )
        
        # 添加城市标记
        city_data = [(city, random.randint(20, 80)) for city in self.cities.keys() if city != "广州"]
        geo.add(
            series_name="城市",
            data_pair=city_data,
            type_=ChartType.SCATTER,
            symbol_size=8,
            itemstyle_opts=opts.ItemStyleOpts(
                color="#4ecdc4",
                opacity=0.8
            )
        )
        
        # 设置全局选项
        geo.set_global_opts(
            title_opts=opts.TitleOpts(
                title="全国业务网络辐射图",
                subtitle=f"阶段 {step + 1}",
                title_textstyle_opts=opts.TextStyleOpts(
                    color="#fff",
                    font_size=24
                ),
                subtitle_textstyle_opts=opts.TextStyleOpts(
                    color="#aaa",
                    font_size=16
                ),
                pos_left="center"
            ),
            legend_opts=opts.LegendOpts(
                is_show=True,
                pos_right="5%",
                pos_top="15%",
                textstyle_opts=opts.TextStyleOpts(color="#fff")
            )
        )
        
        return geo
    
    def create_animation(self, num_steps=8):
        """创建动画"""
        timeline = Timeline(
            init_opts=opts.InitOpts(
                width="1200px", 
                height="800px",
                bg_color="#0f1419"
            )
        )
        
        timeline.add_schema(
            is_auto_play=True,
            play_interval=2000,
            pos_bottom="5%"
        )
        
        # 为每个步骤创建图表
        for i in range(num_steps):
            geo_chart = self.create_geo_map(i)
            timeline.add(geo_chart, f"阶段 {i + 1}")
        
        return timeline
    
    def render(self, filename="simple_china_map.html"):
        """渲染到HTML文件"""
        timeline = self.create_animation()
        timeline.render(filename)
        print(f"动画已保存到: {filename}")
        return filename

def main():
    """主函数"""
    print("🚀 正在创建简化版中国地图动态效果...")
    
    # 创建地图实例
    china_map = SimpleChinaMap()
    
    # 渲染动画
    filename = china_map.render()
    
    print("✅ 动画创建完成！")
    print(f"📁 请打开 {filename} 文件查看效果")
    print("\n🎯 特效说明:")
    print("- 红色大圆点：广州总部")
    print("- 蓝绿色小圆点：各城市节点")
    print("- 动态抛物线：业务连接线")
    print("- 自动播放：每2秒切换一个阶段")
    print("\n💡 提示：在浏览器中打开HTML文件查看完整动画效果")

if __name__ == "__main__":
    main()
