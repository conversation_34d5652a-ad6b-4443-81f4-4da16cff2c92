#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版中国地图动态抛物线效果
包含多种主题和高级动画效果
"""

from pyecharts import options as opts
from pyecharts.charts import Geo, Timeline, Page
from pyecharts.globals import ChartType, SymbolType
import random
import math

class EnhancedChinaMap:
    def __init__(self):
        # 总部坐标
        self.headquarters = [113.2644, 23.1291]  # 广州
        
        # 扩展的城市数据
        self.cities_data = {
            "广州": {"coord": [113.2644, 23.1291], "level": 5, "region": "华南"},
            "北京": {"coord": [116.4074, 39.9042], "level": 5, "region": "华北"},
            "上海": {"coord": [121.4737, 31.2304], "level": 5, "region": "华东"},
            "深圳": {"coord": [114.0579, 22.5431], "level": 4, "region": "华南"},
            "杭州": {"coord": [120.1551, 30.2741], "level": 4, "region": "华东"},
            "南京": {"coord": [118.7674, 32.0415], "level": 4, "region": "华东"},
            "武汉": {"coord": [114.2919, 30.5844], "level": 4, "region": "华中"},
            "成都": {"coord": [104.0665, 30.5723], "level": 4, "region": "西南"},
            "西安": {"coord": [108.9540, 34.2658], "level": 3, "region": "西北"},
            "重庆": {"coord": [106.5516, 29.5630], "level": 4, "region": "西南"},
            "天津": {"coord": [117.1901, 39.1084], "level": 3, "region": "华北"},
            "苏州": {"coord": [120.6197, 31.3089], "level": 3, "region": "华东"},
            "青岛": {"coord": [120.3826, 36.0671], "level": 3, "region": "华东"},
            "长沙": {"coord": [112.9836, 28.1127], "level": 3, "region": "华中"},
            "宁波": {"coord": [121.5440, 29.8683], "level": 3, "region": "华东"},
            "郑州": {"coord": [113.6401, 34.7566], "level": 3, "region": "华中"},
            "济南": {"coord": [117.0009, 36.6758], "level": 3, "region": "华东"},
            "合肥": {"coord": [117.2272, 31.8206], "level": 2, "region": "华东"},
            "福州": {"coord": [119.2965, 26.0745], "level": 2, "region": "华东"},
            "厦门": {"coord": [118.0894, 24.4798], "level": 2, "region": "华东"},
            "昆明": {"coord": [102.8332, 24.8801], "level": 2, "region": "西南"},
            "贵阳": {"coord": [106.7135, 26.5783], "level": 2, "region": "西南"},
            "南宁": {"coord": [108.3669, 22.8176], "level": 2, "region": "华南"},
            "海口": {"coord": [110.3312, 20.0311], "level": 1, "region": "华南"},
            "兰州": {"coord": [103.8236, 36.0581], "level": 2, "region": "西北"},
            "银川": {"coord": [106.2586, 38.4707], "level": 1, "region": "西北"},
            "西宁": {"coord": [101.7782, 36.6171], "level": 1, "region": "西北"},
            "乌鲁木齐": {"coord": [87.6177, 43.7928], "level": 2, "region": "西北"},
            "拉萨": {"coord": [91.1174, 29.6478], "level": 1, "region": "西南"},
            "哈尔滨": {"coord": [126.6420, 45.7560], "level": 3, "region": "东北"},
            "长春": {"coord": [125.3245, 43.8868], "level": 2, "region": "东北"},
            "沈阳": {"coord": [123.4315, 41.8057], "level": 3, "region": "东北"},
        }
        
        # 主题配置
        self.themes = {
            "tech": {
                "bg": "#0a0e27",
                "map": "#1a1a2e", 
                "border": "#16213e",
                "primary": "#00d4ff",
                "secondary": "#ff6b6b",
                "accent": "#4ecdc4",
                "name": "科技蓝"
            },
            "business": {
                "bg": "#1a1a1a",
                "map": "#2d2d2d",
                "border": "#404040", 
                "primary": "#ffd700",
                "secondary": "#ff4757",
                "accent": "#2ed573",
                "name": "商务金"
            },
            "ocean": {
                "bg": "#0c1445",
                "map": "#1e3a8a",
                "border": "#3b82f6",
                "primary": "#06ffa5", 
                "secondary": "#fb7185",
                "accent": "#fbbf24",
                "name": "海洋蓝"
            }
        }
    
    def calculate_distance(self, city1, city2):
        """计算两个城市之间的距离"""
        coord1 = self.cities_data[city1]["coord"]
        coord2 = self.cities_data[city2]["coord"]
        return math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
    
    def generate_wave_connections(self, wave, total_waves=6):
        """生成波浪式连接"""
        cities_list = [city for city in self.cities_data.keys() if city != "广州"]
        
        # 按距离排序
        cities_with_distance = []
        for city in cities_list:
            distance = self.calculate_distance("广州", city)
            cities_with_distance.append((city, distance))
        
        cities_with_distance.sort(key=lambda x: x[1])
        
        # 计算当前波次的城市
        cities_per_wave = len(cities_with_distance) // total_waves + 1
        start_idx = wave * cities_per_wave
        end_idx = min((wave + 1) * cities_per_wave, len(cities_with_distance))
        
        current_wave_cities = cities_with_distance[start_idx:end_idx]
        
        # 生成连接数据
        connections = []
        for city, distance in current_wave_cities:
            connections.append(["广州", city])
        
        return connections
    
    def create_enhanced_geo(self, wave, theme="tech"):
        """创建增强版地理图表"""
        theme_config = self.themes[theme]
        
        geo = (
            Geo(init_opts=opts.InitOpts(
                width="1400px", 
                height="900px",
                bg_color=theme_config["bg"]
            ))
            .add_schema(
                maptype="china",
                itemstyle_opts=opts.ItemStyleOpts(
                    color=theme_config["map"], 
                    border_color=theme_config["border"],
                    border_width=1
                ),
                label_opts=opts.LabelOpts(is_show=False),
                emphasis_itemstyle_opts=opts.ItemStyleOpts(
                    color=theme_config["primary"]
                )
            )
        )
        
        # 添加城市坐标
        for city, data in self.cities_data.items():
            geo.add_coordinate(city, data["coord"][0], data["coord"][1])
        
        # 生成连接数据
        connections = self.generate_wave_connections(wave)
        
        # 添加连接线
        if connections:
            geo.add(
                series_name="业务连接",
                data_pair=connections,
                type_=ChartType.LINES,
                effect_opts=opts.EffectOpts(
                    is_show=True,
                    symbol=SymbolType.ARROW,
                    symbol_size=8,
                    color=theme_config["secondary"],
                    trail_length=0.4,
                    period=3
                ),
                linestyle_opts=opts.LineStyleOpts(
                    curve=0.4,
                    opacity=0.9,
                    width=3,
                    color=theme_config["primary"]
                )
            )
        
        # 添加总部特殊标记
        geo.add(
            series_name="总部",
            data_pair=[("广州", 500)],
            type_=ChartType.SCATTER,
            symbol_size=25,
            itemstyle_opts=opts.ItemStyleOpts(
                color=theme_config["secondary"],
                border_color="#fff",
                border_width=3
            )
        )
        
        # 按城市等级添加不同大小的标记
        for level in range(1, 6):
            level_cities = [(city, data["level"] * 20) for city, data in self.cities_data.items() 
                           if data["level"] == level and city != "广州"]
            
            if level_cities:
                geo.add(
                    series_name=f"{level}级城市",
                    data_pair=level_cities,
                    type_=ChartType.SCATTER,
                    symbol_size=level * 3 + 5,
                    itemstyle_opts=opts.ItemStyleOpts(
                        color=theme_config["accent"],
                        opacity=0.7 + level * 0.05
                    )
                )
        
        # 设置全局选项
        geo.set_global_opts(
            title_opts=opts.TitleOpts(
                title=f"全国智能网络系统 - {theme_config['name']}主题",
                subtitle=f"第 {wave + 1} 波扩散 | 共覆盖 {len(connections)} 个城市",
                title_textstyle_opts=opts.TextStyleOpts(
                    color="#fff",
                    font_size=26,
                    font_weight="bold"
                ),
                subtitle_textstyle_opts=opts.TextStyleOpts(
                    color=theme_config["accent"],
                    font_size=16
                ),
                pos_left="center",
                pos_top="3%"
            ),
            legend_opts=opts.LegendOpts(
                is_show=True,
                pos_right="3%",
                pos_top="12%",
                orient="vertical",
                textstyle_opts=opts.TextStyleOpts(color="#fff", font_size=12)
            )
        )
        
        return geo
    
    def create_theme_animation(self, theme="tech", waves=6):
        """创建主题动画"""
        timeline = Timeline(
            init_opts=opts.InitOpts(
                width="1400px", 
                height="900px",
                bg_color=self.themes[theme]["bg"]
            )
        )
        
        timeline.add_schema(
            is_auto_play=True,
            play_interval=2500,
            pos_bottom="5%"
        )
        
        for wave in range(waves):
            geo_chart = self.create_enhanced_geo(wave, theme)
            timeline.add(geo_chart, f"波次 {wave + 1}")
        
        return timeline
    
    def render_all_themes(self):
        """渲染所有主题"""
        results = []
        
        for theme_name, theme_config in self.themes.items():
            print(f"🎨 正在创建 {theme_config['name']} 主题动画...")
            timeline = self.create_theme_animation(theme_name)
            filename = f"china_map_{theme_name}.html"
            timeline.render(filename)
            results.append(filename)
            print(f"✅ {theme_config['name']} 主题已保存: {filename}")
        
        return results

def main():
    """主函数"""
    print("🚀 正在创建增强版中国地图动态辐射效果...")
    print("=" * 60)
    
    # 创建增强版地图
    enhanced_map = EnhancedChinaMap()
    
    # 渲染所有主题
    files = enhanced_map.render_all_themes()
    
    print("\n🎉 所有动画创建完成！")
    print("\n📁 生成的文件:")
    for file in files:
        print(f"   📄 {file}")
    
    print("\n🎯 特效特点:")
    print("   ✨ 波浪式扩散：从近到远逐步连接")
    print("   🎨 三种主题：科技蓝、商务金、海洋蓝")
    print("   📊 城市分级：按重要程度显示不同大小")
    print("   💫 动态抛物线：流光溢彩的连接效果")
    print("   🎪 自动播放：每2.5秒切换一个波次")
    
    print("\n💡 使用提示:")
    print("   🌐 在浏览器中打开HTML文件查看完整效果")
    print("   ⏯️  可以暂停/播放动画，手动控制进度")
    print("   🔍 支持缩放和拖拽地图")

if __name__ == "__main__":
    main()
