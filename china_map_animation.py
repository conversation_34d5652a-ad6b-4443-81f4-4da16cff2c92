#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国地图动态抛物线辐射效果
从广东总部向全国各省份发射动态抛物线
"""

import numpy as np
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Geo, Timeline
from pyecharts.globals import ChartType, SymbolType
import random
import math

class ChinaMapAnimation:
    def __init__(self):
        # 广东省坐标（总部位置）
        self.headquarters = [113.2644, 23.1291]  # 广州坐标
        
        # 全国主要省份坐标
        self.provinces = {
            "北京": [116.4074, 39.9042],
            "上海": [121.4737, 31.2304],
            "天津": [117.1901, 39.1084],
            "重庆": [106.5516, 29.5630],
            "河北": [114.5149, 38.0428],
            "山西": [112.5489, 37.8570],
            "内蒙古": [111.7656, 40.8175],
            "辽宁": [123.4315, 41.8057],
            "吉林": [125.3245, 43.8868],
            "黑龙江": [126.6420, 45.7560],
            "江苏": [118.7674, 32.0415],
            "浙江": [120.1551, 30.2741],
            "安徽": [117.2272, 31.8206],
            "福建": [119.2965, 26.0745],
            "江西": [115.8921, 28.6765],
            "山东": [117.0009, 36.6758],
            "河南": [113.6401, 34.7566],
            "湖北": [114.2919, 30.5844],
            "湖南": [112.9836, 28.1127],
            "广西": [108.3669, 22.8176],
            "海南": [110.3312, 20.0311],
            "四川": [104.0665, 30.5723],
            "贵州": [106.7135, 26.5783],
            "云南": [102.8332, 24.8801],
            "西藏": [91.1174, 29.6478],
            "陕西": [108.9540, 34.2658],
            "甘肃": [103.8236, 36.0581],
            "青海": [101.7782, 36.6171],
            "宁夏": [106.2586, 38.4707],
            "新疆": [87.6177, 43.7928],
        }
    
    def generate_flight_data(self, time_step):
        """生成飞行线数据"""
        flight_data = []
        
        # 根据时间步长选择不同的目标省份
        provinces_list = list(self.provinces.keys())
        
        # 每个时间步显示不同数量的连线
        num_connections = min(5 + time_step * 2, len(provinces_list))
        
        # 随机选择省份，但确保包含一些重要城市
        important_cities = ["北京", "上海", "江苏", "浙江", "山东", "河南", "湖北", "四川"]
        selected_provinces = important_cities[:min(num_connections//2, len(important_cities))]
        
        # 添加其他随机省份
        remaining = [p for p in provinces_list if p not in selected_provinces]
        selected_provinces.extend(random.sample(remaining, 
                                              min(num_connections - len(selected_provinces), 
                                                  len(remaining))))
        
        for province in selected_provinces:
            if province in self.provinces:
                # 添加一些随机性到数值
                value = random.randint(50, 200)
                flight_data.append({
                    "from_name": "广东",
                    "to_name": province,
                    "coords": [self.headquarters, self.provinces[province]],
                    "value": value
                })
        
        return flight_data
    
    def create_geo_chart(self, time_step):
        """创建地理图表"""
        geo = (
            Geo(init_opts=opts.InitOpts(
                width="1200px", 
                height="800px",
                bg_color="#0f1419"
            ))
            .add_schema(
                maptype="china",
                itemstyle_opts=opts.ItemStyleOpts(
                    color="#323c48", 
                    border_color="#404a59"
                ),
                label_opts=opts.LabelOpts(is_show=False),
                emphasis_itemstyle_opts=opts.ItemStyleOpts(
                    color="#389bb7",
                    border_color="#389bb7"
                )
            )
        )
        
        # 添加总部标记
        geo.add_coordinate("广东总部", self.headquarters[0], self.headquarters[1])
        
        # 添加省份坐标
        for province, coord in self.provinces.items():
            geo.add_coordinate(province, coord[0], coord[1])
        
        # 生成飞行数据
        flight_data = self.generate_flight_data(time_step)
        
        # 添加飞行线
        if flight_data:
            geo.add(
                series_name="",
                data_pair=flight_data,
                type_=ChartType.LINES,
                effect_opts=opts.EffectOpts(
                    is_show=True,
                    brush_type="stroke",
                    scale=6,
                    color="#ff6b6b",
                    symbol=SymbolType.ARROW,
                    symbol_size=8,
                    trail_length=0.4,
                    period=4,
                ),
                linestyle_opts=opts.LineStyleOpts(
                    curve=0.3,  # 抛物线弧度
                    opacity=0.8,
                    width=2,
                    color="#4ecdc4"
                ),
                symbol_size=[8, 12],
                symbol=["circle", "arrow"]
            )
        
        # 添加总部特殊标记
        geo.add(
            series_name="总部",
            data_pair=[("广东总部", 100)],
            type_=ChartType.SCATTER,
            symbol_size=20,
            itemstyle_opts=opts.ItemStyleOpts(
                color="#ff6b6b",
                border_color="#fff",
                border_width=2
            ),
            effect_opts=opts.EffectOpts(
                is_show=True,
                brush_type="fill",
                scale=4,
                color="#ff6b6b",
                period=2,
            )
        )
        
        # 添加省份标记点
        province_data = [(name, random.randint(20, 80)) for name in self.provinces.keys()]
        geo.add(
            series_name="省份",
            data_pair=province_data,
            type_=ChartType.SCATTER,
            symbol_size=8,
            itemstyle_opts=opts.ItemStyleOpts(
                color="#4ecdc4",
                opacity=0.8
            )
        )
        
        geo.set_global_opts(
            title_opts=opts.TitleOpts(
                title="全国业务网络辐射图",
                subtitle=f"时间步: {time_step + 1}",
                title_textstyle_opts=opts.TextStyleOpts(
                    color="#fff",
                    font_size=24
                ),
                subtitle_textstyle_opts=opts.TextStyleOpts(
                    color="#aaa",
                    font_size=16
                ),
                pos_left="center"
            ),
            legend_opts=opts.LegendOpts(
                is_show=False
            ),
            visualmap_opts=opts.VisualMapOpts(
                is_show=False
            )
        )
        
        return geo
    
    def create_timeline_animation(self, num_frames=10):
        """创建时间轴动画"""
        timeline = Timeline(
            init_opts=opts.InitOpts(
                width="1200px", 
                height="800px",
                bg_color="#0f1419"
            )
        )
        
        timeline.add_schema(
            orient="horizontal",
            is_auto_play=True,
            is_inverse=False,
            play_interval=2000,  # 2秒切换一次
            pos_left="5%",
            pos_right="5%",
            pos_bottom="5%",
            label_opts=opts.LabelOpts(
                color="#fff",
                font_size=12
            ),
            itemstyle_opts=opts.ItemStyleOpts(
                color="#4ecdc4"
            )
        )
        
        # 为每个时间步创建图表
        for i in range(num_frames):
            geo_chart = self.create_geo_chart(i)
            timeline.add(geo_chart, f"阶段 {i + 1}")
        
        return timeline
    
    def render_animation(self, filename="china_map_animation.html"):
        """渲染动画到HTML文件"""
        timeline = self.create_timeline_animation()
        timeline.render(filename)
        print(f"动画已保存到: {filename}")
        return filename

def main():
    """主函数"""
    print("正在创建中国地图动态辐射效果...")
    
    # 创建动画实例
    animation = ChinaMapAnimation()
    
    # 渲染动画
    filename = animation.render_animation()
    
    print("动画创建完成！")
    print(f"请打开 {filename} 文件查看效果")
    print("\n特效说明:")
    print("- 红色闪烁点：广东总部")
    print("- 蓝绿色点：各省份节点")
    print("- 动态抛物线：业务辐射连线")
    print("- 自动播放：每2秒切换一个阶段")

if __name__ == "__main__":
    main()
