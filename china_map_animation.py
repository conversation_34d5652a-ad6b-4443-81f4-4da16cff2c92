#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国地图动态抛物线辐射效果 - 海洋蓝主题
从广东总部向全国各省份发射动态抛物线，3波次扩散
"""

import math
import random
from pyecharts import options as opts
from pyecharts.charts import Geo, Timeline
from pyecharts.globals import ChartType, SymbolType

class ChinaMapAnimation:
    def __init__(self):
        # 广东省坐标（总部位置）
        self.headquarters = [113.2644, 23.1291]  # 广州坐标

        # 全国主要省份坐标
        self.provinces = {
            "北京": [116.4074, 39.9042],
            "上海": [121.4737, 31.2304],
            "天津": [117.1901, 39.1084],
            "重庆": [106.5516, 29.5630],
            "河北": [114.5149, 38.0428],
            "山西": [112.5489, 37.8570],
            "内蒙古": [111.7656, 40.8175],
            "辽宁": [123.4315, 41.8057],
            "吉林": [125.3245, 43.8868],
            "黑龙江": [126.6420, 45.7560],
            "江苏": [118.7674, 32.0415],
            "浙江": [120.1551, 30.2741],
            "安徽": [117.2272, 31.8206],
            "福建": [119.2965, 26.0745],
            "江西": [115.8921, 28.6765],
            "山东": [117.0009, 36.6758],
            "河南": [113.6401, 34.7566],
            "湖北": [114.2919, 30.5844],
            "湖南": [112.9836, 28.1127],
            "广西": [108.3669, 22.8176],
            "海南": [110.3312, 20.0311],
            "四川": [104.0665, 30.5723],
            "贵州": [106.7135, 26.5783],
            "云南": [102.8332, 24.8801],
            "西藏": [91.1174, 29.6478],
            "陕西": [108.9540, 34.2658],
            "甘肃": [103.8236, 36.0581],
            "青海": [101.7782, 36.6171],
            "宁夏": [106.2586, 38.4707],
            "新疆": [87.6177, 43.7928],
        }

        # 海洋蓝主题配色
        self.theme = {
            "bg": "#0c1445",
            "map": "#1e3a8a",
            "border": "#3b82f6",
            "primary": "#06ffa5",
            "secondary": "#fb7185",
            "accent": "#fbbf24"
        }
    
    def calculate_distance(self, province):
        """计算省份到总部的距离"""
        coord = self.provinces[province]
        return math.sqrt((coord[0] - self.headquarters[0])**2 + (coord[1] - self.headquarters[1])**2)

    def generate_wave_data(self, wave_number):
        """生成3波次扩散数据"""
        provinces_list = list(self.provinces.keys())

        # 按距离排序省份
        provinces_with_distance = []
        for province in provinces_list:
            distance = self.calculate_distance(province)
            provinces_with_distance.append((province, distance))

        provinces_with_distance.sort(key=lambda x: x[1])

        # 分为3波次
        total_provinces = len(provinces_with_distance)
        wave_size = total_provinces // 3

        if wave_number == 0:  # 第一波：最近的省份
            selected = provinces_with_distance[:wave_size]
        elif wave_number == 1:  # 第二波：中等距离的省份
            selected = provinces_with_distance[wave_size:wave_size*2]
        else:  # 第三波：最远的省份
            selected = provinces_with_distance[wave_size*2:]

        # 生成连接数据
        flight_data = []
        for province, distance in selected:
            flight_data.append(["广东总部", province])

        return flight_data, [p[0] for p in selected]
    
    def create_geo_chart(self, wave_number):
        """创建地理图表"""
        geo = (
            Geo(init_opts=opts.InitOpts(
                width="1400px",
                height="900px",
                bg_color=self.theme["bg"]
            ))
            .add_schema(
                maptype="china",
                itemstyle_opts=opts.ItemStyleOpts(
                    color=self.theme["map"],
                    border_color=self.theme["border"],
                    border_width=1
                ),
                label_opts=opts.LabelOpts(
                    is_show=True,
                    color="#fff",
                    font_size=10
                ),
                emphasis_itemstyle_opts=opts.ItemStyleOpts(
                    color=self.theme["primary"],
                    border_color=self.theme["accent"]
                )
            )
        )
        
        # 添加总部标记
        geo.add_coordinate("广东总部", self.headquarters[0], self.headquarters[1])

        # 添加省份坐标
        for province, coord in self.provinces.items():
            geo.add_coordinate(province, coord[0], coord[1])

        # 生成波次数据
        flight_data, current_provinces = self.generate_wave_data(wave_number)

        # 添加飞行线
        if flight_data:
            geo.add(
                series_name="业务连接",
                data_pair=flight_data,
                type_=ChartType.LINES,
                effect_opts=opts.EffectOpts(
                    is_show=True,
                    brush_type="stroke",
                    scale=8,
                    color=self.theme["secondary"],
                    symbol=SymbolType.ARROW,
                    symbol_size=10,
                    trail_length=0.6,
                    period=3,
                ),
                linestyle_opts=opts.LineStyleOpts(
                    curve=0.4,  # 抛物线弧度
                    opacity=0.9,
                    width=3,
                    color=self.theme["primary"]
                ),
                symbol_size=[10, 15],
                symbol=["circle", "arrow"]
            )
        
        # 添加总部特殊标记
        geo.add(
            series_name="总部",
            data_pair=[("广东总部", 500)],
            type_=ChartType.SCATTER,
            symbol_size=25,
            itemstyle_opts=opts.ItemStyleOpts(
                color=self.theme["secondary"],
                border_color="#fff",
                border_width=3
            ),
            effect_opts=opts.EffectOpts(
                is_show=True,
                brush_type="fill",
                scale=6,
                color=self.theme["secondary"],
                period=2,
            )
        )

        # 添加当前波次的省份标记（高亮显示）
        current_province_data = [(name, 100) for name in current_provinces]
        if current_province_data:
            geo.add(
                series_name="当前连接省份",
                data_pair=current_province_data,
                type_=ChartType.SCATTER,
                symbol_size=12,
                itemstyle_opts=opts.ItemStyleOpts(
                    color=self.theme["accent"],
                    border_color=self.theme["primary"],
                    border_width=2,
                    opacity=0.9
                )
            )

        # 添加其他省份标记点（较暗显示）
        other_provinces = [name for name in self.provinces.keys() if name not in current_provinces]
        other_province_data = [(name, 50) for name in other_provinces]
        if other_province_data:
            geo.add(
                series_name="其他省份",
                data_pair=other_province_data,
                type_=ChartType.SCATTER,
                symbol_size=6,
                itemstyle_opts=opts.ItemStyleOpts(
                    color=self.theme["primary"],
                    opacity=0.4
                )
            )
        
        # 波次描述
        wave_descriptions = ["近距离省份", "中距离省份", "远距离省份"]
        current_description = wave_descriptions[wave_number] if wave_number < 3 else "全部省份"

        geo.set_global_opts(
            title_opts=opts.TitleOpts(
                title="全国业务网络辐射图 - 海洋蓝主题",
                subtitle=f"第 {wave_number + 1} 波扩散：{current_description} | 连接 {len(current_provinces)} 个省份",
                title_textstyle_opts=opts.TextStyleOpts(
                    color="#fff",
                    font_size=26,
                    font_weight="bold"
                ),
                subtitle_textstyle_opts=opts.TextStyleOpts(
                    color=self.theme["accent"],
                    font_size=16
                ),
                pos_left="center",
                pos_top="3%"
            ),
            legend_opts=opts.LegendOpts(
                is_show=True,
                pos_right="3%",
                pos_top="12%",
                orient="vertical",
                textstyle_opts=opts.TextStyleOpts(color="#fff", font_size=12)
            )
        )
        
        return geo
    
    def create_timeline_animation(self):
        """创建3波次时间轴动画"""
        timeline = Timeline(
            init_opts=opts.InitOpts(
                width="1400px",
                height="900px",
                bg_color=self.theme["bg"]
            )
        )

        timeline.add_schema(
            orient="horizontal",
            is_auto_play=True,
            is_inverse=False,
            play_interval=3000,  # 3秒切换一次
            pos_left="10%",
            pos_right="10%",
            pos_bottom="5%",
            label_opts=opts.LabelOpts(
                color="#fff",
                font_size=14
            ),
            itemstyle_opts=opts.ItemStyleOpts(
                color=self.theme["accent"]
            )
        )

        # 创建3个波次的图表
        wave_names = ["第一波：近距离", "第二波：中距离", "第三波：远距离"]
        for i in range(3):
            geo_chart = self.create_geo_chart(i)
            timeline.add(geo_chart, wave_names[i])

        return timeline
    
    def render_animation(self, filename="china_map_ocean_theme.html"):
        """渲染动画到HTML文件"""
        timeline = self.create_timeline_animation()
        timeline.render(filename)
        print(f"🌊 海洋蓝主题动画已保存到: {filename}")
        return filename

def main():
    """主函数"""
    print("🚀 正在创建中国地图动态辐射效果 - 海洋蓝主题")
    print("=" * 60)

    # 创建动画实例
    animation = ChinaMapAnimation()

    # 渲染动画
    filename = animation.render_animation()

    print("✅ 动画创建完成！")
    print(f"📁 请打开 {filename} 文件查看效果")
    print("\n🎯 特效说明:")
    print("   🌊 海洋蓝主题：深海蓝背景配色")
    print("   🎯 粉色闪烁点：广东总部")
    print("   💎 金色高亮点：当前波次连接的省份")
    print("   🔵 青色暗点：其他省份节点")
    print("   ⚡ 青绿色抛物线：业务辐射连线")
    print("   📊 省份名字：地图上显示省份标签")
    print("   🎪 3波次扩散：按距离远近分3波连接")
    print("   ⏱️  自动播放：每3秒切换一个波次")

    print("\n💡 使用提示:")
    print("   🌐 在浏览器中打开HTML文件查看完整效果")
    print("   ⏯️  可以暂停/播放动画，手动控制进度")
    print("   🔍 支持缩放和拖拽地图")

if __name__ == "__main__":
    main()
