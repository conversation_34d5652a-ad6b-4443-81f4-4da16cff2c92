#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级中国地图动态效果
包含多种动画模式和自定义配置
"""

import numpy as np
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Geo, Timeline, Page
from pyecharts.globals import ChartType, SymbolType
import random
import math
import json

class AdvancedChinaMap:
    def __init__(self):
        self.headquarters = [113.2644, 23.1291]  # 广州
        
        # 扩展的城市数据，包含经济数据
        self.cities_data = {
            "北京": {"coord": [116.4074, 39.9042], "gdp": 4000, "population": 2154},
            "上海": {"coord": [121.4737, 31.2304], "gdp": 4300, "population": 2428},
            "深圳": {"coord": [114.0579, 22.5431], "gdp": 3200, "population": 1756},
            "广州": {"coord": [113.2644, 23.1291], "gdp": 2800, "population": 1868},
            "杭州": {"coord": [120.1551, 30.2741], "gdp": 1810, "population": 1196},
            "南京": {"coord": [118.7674, 32.0415], "gdp": 1640, "population": 850},
            "武汉": {"coord": [114.2919, 30.5844], "gdp": 1770, "population": 1121},
            "成都": {"coord": [104.0665, 30.5723], "gdp": 1990, "population": 1658},
            "西安": {"coord": [108.9540, 34.2658], "gdp": 1020, "population": 1000},
            "重庆": {"coord": [106.5516, 29.5630], "gdp": 2500, "population": 3124},
            "天津": {"coord": [117.1901, 39.1084], "gdp": 1400, "population": 1386},
            "苏州": {"coord": [120.6197, 31.3089], "gdp": 2200, "population": 1275},
            "青岛": {"coord": [120.3826, 36.0671], "gdp": 1400, "population": 1007},
            "长沙": {"coord": [112.9836, 28.1127], "gdp": 1320, "population": 1004},
            "宁波": {"coord": [121.5440, 29.8683], "gdp": 1240, "population": 854},
            "郑州": {"coord": [113.6401, 34.7566], "gdp": 1200, "population": 1260},
            "佛山": {"coord": [113.1220, 23.0291], "gdp": 1070, "population": 950},
            "济南": {"coord": [117.0009, 36.6758], "gdp": 1140, "population": 890},
            "东莞": {"coord": [113.7518, 23.0489], "gdp": 950, "population": 1047},
            "合肥": {"coord": [117.2272, 31.8206], "gdp": 1000, "population": 937},
        }
        
        # 颜色主题
        self.themes = {
            "tech": {
                "bg": "#0a0e27",
                "map": "#1a1a2e",
                "border": "#16213e",
                "primary": "#00d4ff",
                "secondary": "#ff6b6b",
                "accent": "#4ecdc4"
            },
            "business": {
                "bg": "#1a1a1a",
                "map": "#2d2d2d",
                "border": "#404040",
                "primary": "#ffd700",
                "secondary": "#ff4757",
                "accent": "#2ed573"
            },
            "ocean": {
                "bg": "#0c1445",
                "map": "#1e3a8a",
                "border": "#3b82f6",
                "primary": "#06ffa5",
                "secondary": "#fb7185",
                "accent": "#fbbf24"
            }
        }
    
    def calculate_connection_strength(self, city1, city2):
        """根据城市经济数据计算连接强度"""
        gdp1 = self.cities_data.get(city1, {}).get("gdp", 100)
        gdp2 = self.cities_data.get(city2, {}).get("gdp", 100)
        
        # 基于GDP计算连接强度
        strength = math.sqrt(gdp1 * gdp2) / 100
        return min(max(strength, 20), 200)
    
    def generate_wave_data(self, wave_number, total_waves=8):
        """生成波浪式扩散数据"""
        flight_data = []
        cities_list = list(self.cities_data.keys())
        
        # 移除广州（总部）
        if "广州" in cities_list:
            cities_list.remove("广州")
        
        # 按距离分组
        distances = []
        for city in cities_list:
            coord = self.cities_data[city]["coord"]
            dist = math.sqrt((coord[0] - self.headquarters[0])**2 + 
                           (coord[1] - self.headquarters[1])**2)
            distances.append((city, dist))
        
        # 按距离排序
        distances.sort(key=lambda x: x[1])
        
        # 计算每波包含的城市数量
        cities_per_wave = len(distances) // total_waves + 1
        
        # 选择当前波次的城市
        start_idx = wave_number * cities_per_wave
        end_idx = min((wave_number + 1) * cities_per_wave, len(distances))
        
        current_wave_cities = distances[start_idx:end_idx]
        
        for city, dist in current_wave_cities:
            if city in self.cities_data:
                strength = self.calculate_connection_strength("广州", city)
                flight_data.append({
                    "from_name": "广州",
                    "to_name": city,
                    "coords": [self.headquarters, self.cities_data[city]["coord"]],
                    "value": strength
                })
        
        return flight_data
    
    def create_advanced_geo(self, wave_number, theme="tech"):
        """创建高级地理图表"""
        theme_colors = self.themes[theme]
        
        geo = (
            Geo(init_opts=opts.InitOpts(
                width="1400px", 
                height="900px",
                bg_color=theme_colors["bg"]
            ))
            .add_schema(
                maptype="china",
                itemstyle_opts=opts.ItemStyleOpts(
                    color=theme_colors["map"], 
                    border_color=theme_colors["border"],
                    border_width=1
                ),
                label_opts=opts.LabelOpts(is_show=False),
                emphasis_itemstyle_opts=opts.ItemStyleOpts(
                    color=theme_colors["primary"],
                    border_color=theme_colors["accent"]
                )
            )
        )
        
        # 添加城市坐标
        for city, data in self.cities_data.items():
            geo.add_coordinate(city, data["coord"][0], data["coord"][1])
        
        # 生成波浪数据
        flight_data = self.generate_wave_data(wave_number)
        
        # 添加飞行线效果
        if flight_data:
            geo.add(
                series_name="连接线",
                data_pair=flight_data,
                type_=ChartType.LINES,
                effect_opts=opts.EffectOpts(
                    is_show=True,
                    brush_type="stroke",
                    scale=8,
                    color=theme_colors["secondary"],
                    symbol=SymbolType.ARROW,
                    symbol_size=10,
                    trail_length=0.6,
                    period=3,
                ),
                linestyle_opts=opts.LineStyleOpts(
                    curve=0.4,
                    opacity=0.9,
                    width=3,
                    color=theme_colors["primary"]
                ),
                symbol_size=[10, 15],
                symbol=["circle", "arrow"]
            )
        
        # 添加总部超级标记
        geo.add(
            series_name="总部",
            data_pair=[("广州", 500)],
            type_=ChartType.SCATTER,
            symbol_size=25,
            itemstyle_opts=opts.ItemStyleOpts(
                color=theme_colors["secondary"],
                border_color="#fff",
                border_width=3,
                opacity=0.9
            ),
            effect_opts=opts.EffectOpts(
                is_show=True,
                brush_type="fill",
                scale=6,
                color=theme_colors["secondary"],
                period=1.5,
            )
        )
        
        # 添加城市节点（按GDP大小）
        city_scatter_data = []
        for city, data in self.cities_data.items():
            if city != "广州":  # 排除总部
                size = max(8, min(20, data["gdp"] / 100))
                city_scatter_data.append((city, data["gdp"]))
        
        geo.add(
            series_name="城市节点",
            data_pair=city_scatter_data,
            type_=ChartType.SCATTER,
            symbol_size=lambda x: max(8, min(20, x / 100)),
            itemstyle_opts=opts.ItemStyleOpts(
                color=theme_colors["accent"],
                opacity=0.8,
                border_color=theme_colors["primary"],
                border_width=1
            )
        )
        
        # 设置全局选项
        geo.set_global_opts(
            title_opts=opts.TitleOpts(
                title="全国智能网络辐射系统",
                subtitle=f"第 {wave_number + 1} 波扩散 | 主题: {theme.upper()}",
                title_textstyle_opts=opts.TextStyleOpts(
                    color="#fff",
                    font_size=28,
                    font_weight="bold"
                ),
                subtitle_textstyle_opts=opts.TextStyleOpts(
                    color=theme_colors["accent"],
                    font_size=18
                ),
                pos_left="center",
                pos_top="3%"
            ),
            legend_opts=opts.LegendOpts(
                is_show=True,
                pos_right="5%",
                pos_top="15%",
                orient="vertical",
                textstyle_opts=opts.TextStyleOpts(color="#fff")
            ),
            visualmap_opts=opts.VisualMapOpts(
                is_show=True,
                type_="size",
                range_size=[10, 30],
                range_color=[theme_colors["accent"], theme_colors["primary"]],
                pos_right="5%",
                pos_bottom="15%",
                textstyle_opts=opts.TextStyleOpts(color="#fff"),
                title="城市规模"
            )
        )
        
        return geo
    
    def create_multi_theme_animation(self):
        """创建多主题动画"""
        page = Page(layout=Page.SimplePageLayout)
        
        themes = ["tech", "business", "ocean"]
        
        for theme in themes:
            timeline = Timeline(
                init_opts=opts.InitOpts(
                    width="1400px", 
                    height="900px",
                    bg_color=self.themes[theme]["bg"]
                )
            )
            
            timeline.add_schema(
                orient="horizontal",
                is_auto_play=True,
                is_inverse=False,
                play_interval=2500,
                pos_left="10%",
                pos_right="10%",
                pos_bottom="8%",
                label_opts=opts.LabelOpts(
                    color="#fff",
                    font_size=14
                ),
                itemstyle_opts=opts.ItemStyleOpts(
                    color=self.themes[theme]["accent"]
                )
            )
            
            # 为每个主题创建8个波次
            for wave in range(8):
                geo_chart = self.create_advanced_geo(wave, theme)
                timeline.add(geo_chart, f"波次 {wave + 1}")
            
            page.add(timeline)
        
        return page

def main():
    """主函数"""
    print("🚀 正在创建高级中国地图动态辐射效果...")
    
    # 创建高级动画实例
    advanced_map = AdvancedChinaMap()
    
    # 创建单主题动画
    print("📊 创建科技主题动画...")
    timeline_tech = Timeline(init_opts=opts.InitOpts(width="1400px", height="900px"))
    timeline_tech.add_schema(is_auto_play=True, play_interval=2000)
    
    for i in range(8):
        geo = advanced_map.create_advanced_geo(i, "tech")
        timeline_tech.add(geo, f"波次 {i + 1}")
    
    timeline_tech.render("china_map_tech_theme.html")
    print("✅ 科技主题动画已保存: china_map_tech_theme.html")
    
    # 创建多主题页面
    print("🎨 创建多主题动画页面...")
    multi_page = advanced_map.create_multi_theme_animation()
    multi_page.render("china_map_multi_themes.html")
    print("✅ 多主题动画已保存: china_map_multi_themes.html")
    
    print("\n🎉 所有动画创建完成！")
    print("\n📋 文件说明:")
    print("- china_map_tech_theme.html: 科技主题单页动画")
    print("- china_map_multi_themes.html: 包含三种主题的完整版本")
    print("\n🎯 特效特点:")
    print("- 波浪式扩散：从近到远逐步连接城市")
    print("- 多主题切换：科技、商务、海洋三种视觉风格")
    print("- 智能连接：基于城市GDP计算连接强度")
    print("- 动态效果：抛物线、闪烁、流光等多种特效")

if __name__ == "__main__":
    main()
