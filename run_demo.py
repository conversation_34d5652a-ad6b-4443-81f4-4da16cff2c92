#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速演示脚本
"""

import os
import sys
import subprocess

def install_requirements():
    """安装依赖"""
    print("📦 正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败，请手动安装：pip install pyecharts numpy pandas")
        return False

def run_ocean_theme_demo():
    """运行海洋蓝主题演示"""
    print("\n🌊 运行海洋蓝主题中国地图动画...")
    try:
        from china_map_animation import main as ocean_main
        ocean_main()
        return True
    except Exception as e:
        print(f"❌ 海洋蓝主题演示运行失败: {e}")
        return False

def open_results():
    """打开结果文件"""
    html_files = [
        "china_map_ocean_theme.html"
    ]

    print("\n📂 生成的文件:")
    for file in html_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} (未生成)")

    print("\n💡 提示：双击HTML文件在浏览器中查看动画效果")

def main():
    """主函数"""
    print("🌊 中国地图动态辐射效果演示 - 海洋蓝主题")
    print("=" * 60)

    # 检查并安装依赖
    if not install_requirements():
        return

    # 运行海洋蓝主题演示
    print("\n🚀 开始运行海洋蓝主题动画...")
    run_ocean_theme_demo()

    # 显示结果
    open_results()

if __name__ == "__main__":
    main()
