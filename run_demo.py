#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速演示脚本
"""

import os
import sys
import subprocess

def install_requirements():
    """安装依赖"""
    print("📦 正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败，请手动安装：pip install pyecharts numpy pandas")
        return False

def run_basic_demo():
    """运行基础演示"""
    print("\n🎬 运行基础中国地图动画...")
    try:
        from china_map_animation import main as basic_main
        basic_main()
        return True
    except Exception as e:
        print(f"❌ 基础演示运行失败: {e}")
        return False

def run_advanced_demo():
    """运行高级演示"""
    print("\n🚀 运行高级中国地图动画...")
    try:
        from advanced_china_map import main as advanced_main
        advanced_main()
        return True
    except Exception as e:
        print(f"❌ 高级演示运行失败: {e}")
        return False

def open_results():
    """打开结果文件"""
    html_files = [
        "china_map_animation.html",
        "china_map_tech_theme.html", 
        "china_map_multi_themes.html"
    ]
    
    print("\n📂 生成的文件:")
    for file in html_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} (未生成)")
    
    print("\n💡 提示：双击HTML文件在浏览器中查看动画效果")

def main():
    """主函数"""
    print("🌟 中国地图动态辐射效果演示")
    print("=" * 50)
    
    # 检查并安装依赖
    if not install_requirements():
        return
    
    # 运行演示
    print("\n请选择要运行的演示:")
    print("1. 基础版本 (简单动画)")
    print("2. 高级版本 (多主题、多特效)")
    print("3. 全部运行")
    
    choice = input("\n请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        run_basic_demo()
    elif choice == "2":
        run_advanced_demo()
    elif choice == "3":
        run_basic_demo()
        run_advanced_demo()
    else:
        print("❌ 无效选择，运行全部演示...")
        run_basic_demo()
        run_advanced_demo()
    
    # 显示结果
    open_results()

if __name__ == "__main__":
    main()
